import 'dart:io';

import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_coverage_stats.dart';
import 'package:bloomg_flutter/features/face_verification/models/face_detection_result.dart';
import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';
import 'package:bloomg_flutter/features/face_verification/repository/face_detection_repository.dart';
import 'package:bloomg_flutter/features/face_verification/repository/video_storage_repository.dart';
import 'package:bloomg_flutter/features/face_verification/services/camera_service.dart';
import 'package:bloomg_flutter/features/face_verification/services/video_validation_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes for dependencies
class MockFaceDetectionRepository extends Mock
    implements FaceDetectionRepository {}

class MockVideoValidationService extends Mock
    implements VideoValidationService {}

class MockCameraService extends Mock implements CameraService {}

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('[FACE_VERIFICATION] CamerAwesome Integration Tests', () {
    late FaceVideoCaptureBloc bloc;
    late MockFaceDetectionRepository mockFaceDetectionRepository;
    late VideoStorageRepository realVideoStorageRepository;
    late MockVideoValidationService mockVideoValidationService;
    late Directory tempDir;

    setUpAll(() {
      registerFallbackValue(const VideoCaptureConfig());
      registerFallbackValue(const Duration(seconds: 1));
      registerFallbackValue(<FaceDetectionResult>[]);
    });

    setUp(() {
      mockFaceDetectionRepository = MockFaceDetectionRepository();
      mockVideoValidationService = MockVideoValidationService();
      tempDir = Directory.systemTemp.createTempSync('camera_integration_test_');

      // Create a real VideoStorageRepository instance for actual file operations
      realVideoStorageRepository = VideoStorageRepository();

      // Default mock behaviors
      when(() => mockFaceDetectionRepository.initialize())
          .thenAnswer((_) async {});
      when(() => mockFaceDetectionRepository.dispose())
          .thenAnswer((_) async {});

      // Mock video validation service to return good quality stats
      when(
        () => mockVideoValidationService.calculateCoverageStatsWithQuality(
          any(),
          any(),
        ),
      ).thenReturn(
        const FaceCoverageStats(
          totalFrames: 100,
          framesWithFace: 85,
          framesWithValidCoverage: 80,
          averageCoverage: 82.5,
          minimumCoverage: 70,
          maximumCoverage: 95,
          recordingDuration: Duration(seconds: 9),
          detectionResults: <FaceDetectionResult>[],
          qualityScore: 85, // Above 70% threshold
        ),
      );

      bloc = FaceVideoCaptureBloc(
        faceDetectionRepository: mockFaceDetectionRepository,
        videoStorageRepository: realVideoStorageRepository,
        videoValidationService: mockVideoValidationService,
      );
    });

    tearDown(() {
      bloc.close();
      tempDir.deleteSync(recursive: true);
    });

    group('[FACE_VERIFICATION] Real CamerAwesome File Creation', () {
      test('should investigate why CamerAwesome is not creating video files',
          () async {
        // This test investigates the core production issue:
        // CamerAwesome is not creating video files at the expected path

        final states = <FaceVideoCaptureState>[];
        final subscription = bloc.stream.listen(states.add);

        // Act - Initialize camera
        bloc.add(const InitializeCamera());
        await Future<void>.delayed(const Duration(milliseconds: 500));

        // Verify camera is ready
        expect(states.any((state) => state is CameraReady), isTrue);

        // Start recording
        bloc.add(const StartRecording());
        await Future<void>.delayed(const Duration(milliseconds: 500));

        // Verify recording started
        expect(states.any((state) => state is Recording), isTrue);

        // Stop recording
        bloc.add(const StopRecording());
        await Future<void>.delayed(const Duration(milliseconds: 500));

        // Verify processing started
        expect(states.any((state) => state is Processing), isTrue);

        // Get the expected video path from the repository
        final expectedPath = realVideoStorageRepository.currentVideoPath;
        expect(expectedPath, isNotNull);
        expect(expectedPath, isNotEmpty);

        // Check if the video file actually exists at the expected path
        final videoFile = File(expectedPath!);
        final fileExists = await videoFile.exists();

        // Log the investigation results
        print('[CAMERA_INTEGRATION] Investigation Results:');
        print('  Expected video path: $expectedPath');
        print('  File exists: $fileExists');
        if (fileExists) {
          final fileSize = await videoFile.length();
          print('  File size: ${fileSize}B');
        }

        // Check the directory structure
        final parentDir = videoFile.parent;
        final parentExists = await parentDir.exists();
        print('  Parent directory exists: $parentExists');
        if (parentExists) {
          final files = await parentDir.list().toList();
          print('  Files in directory: ${files.map((f) => f.path).toList()}');
        }

        // This test documents the current behavior for investigation
        // In production, this is where the "Video file not found" error occurs
        if (!fileExists) {
          print('[CAMERA_INTEGRATION] ISSUE CONFIRMED: '
              'CamerAwesome did not create video file at expected path');
          print('[CAMERA_INTEGRATION] This is the root cause of the '
              'production "Video file not found" error');
        }

        await subscription.cancel();
      });

      test('should test CamerAwesome configuration and initialization',
          () async {
        // This test examines the CamerAwesome configuration to identify
        // potential issues with video recording setup

        final states = <FaceVideoCaptureState>[];
        final subscription = bloc.stream.listen(states.add);

        // Initialize camera and examine the configuration
        bloc.add(const InitializeCamera());
        await Future<void>.delayed(const Duration(milliseconds: 500));

        // Verify initialization succeeded
        expect(states.any((state) => state is CameraReady), isTrue);

        // Check the video storage repository configuration
        final videoPath = realVideoStorageRepository.currentVideoPath;
        print('[CAMERA_INTEGRATION] Video Storage Configuration:');
        print('  Current video path: $videoPath');

        // Test if the repository can create the directory structure
        if (videoPath != null) {
          final videoFile = File(videoPath);
          final parentDir = videoFile.parent;

          try {
            await parentDir.create(recursive: true);
            print('  Directory creation: SUCCESS');
            print('  Directory path: ${parentDir.path}');
          } catch (e) {
            print('  Directory creation: FAILED - $e');
          }
        }

        await subscription.cancel();
      });

      test('should test manual video file creation to verify path validity',
          () async {
        // This test manually creates a video file at the expected path
        // to verify that the path and directory structure are valid

        final states = <FaceVideoCaptureState>[];
        final subscription = bloc.stream.listen(states.add);

        // Initialize camera
        bloc.add(const InitializeCamera());
        await Future<void>.delayed(const Duration(milliseconds: 500));

        expect(states.any((state) => state is CameraReady), isTrue);

        // Get the expected video path
        final expectedPath = realVideoStorageRepository.currentVideoPath;
        expect(expectedPath, isNotNull);

        // Manually create a test video file at the expected path
        final videoFile = File(expectedPath!);
        await videoFile.parent.create(recursive: true);
        await videoFile.writeAsBytes([
          // Minimal MP4 header for testing
          0x00, 0x00, 0x00, 0x20, // Box size
          0x66, 0x74, 0x79, 0x70, // 'ftyp' signature
          0x69, 0x73, 0x6F, 0x6D, // Brand
          0x00, 0x00, 0x02, 0x00, // Minor version
          // Add some dummy video data
          ...List.generate(1000, (i) => i % 256),
        ]);

        // Verify the file was created successfully
        final fileExists = await videoFile.exists();
        final fileSize = await videoFile.length();

        print('[CAMERA_INTEGRATION] Manual File Creation Test:');
        print('  File created: $fileExists');
        print('  File size: ${fileSize}B');
        print('  File path: ${videoFile.path}');

        expect(fileExists, isTrue);
        expect(fileSize, greaterThan(0));

        // Now test if the BLoC can process this manually created file
        bloc.add(VideoRecordingCompleted(videoPath: expectedPath));
        await Future<void>.delayed(const Duration(milliseconds: 1000));

        // Check if the BLoC successfully processed the file
        final hasSuccess = states.any((state) => state is Success);
        final hasFailure = states.any((state) => state is Failure);
        final hasError = states.any((state) => state is Error);

        print('[CAMERA_INTEGRATION] BLoC Processing Results:');
        print('  Success state: $hasSuccess');
        print('  Failure state: $hasFailure');
        print('  Error state: $hasError');

        // Clean up the test file
        if (await videoFile.exists()) {
          await videoFile.delete();
        }

        await subscription.cancel();
      });
    });

    group('[FACE_VERIFICATION] CamerAwesome Integration Analysis', () {
      test('should analyze the video recording workflow timing', () async {
        // This test analyzes the timing between recording stop and
        // VideoRecordingCompleted event to understand the timeout issue

        final states = <FaceVideoCaptureState>[];
        final subscription = bloc.stream.listen(states.add);
        final stopwatch = Stopwatch();

        // Initialize and start recording
        bloc.add(const InitializeCamera());
        await Future<void>.delayed(const Duration(milliseconds: 500));

        bloc.add(const StartRecording());
        await Future<void>.delayed(const Duration(milliseconds: 500));

        // Stop recording and start timing
        stopwatch.start();
        bloc.add(const StopRecording());

        // Wait for processing state
        await Future<void>.delayed(const Duration(milliseconds: 100));
        expect(states.any((state) => state is Processing), isTrue);

        // Wait for timeout (1 second) and measure
        await Future<void>.delayed(const Duration(milliseconds: 1200));
        stopwatch.stop();

        print('[CAMERA_INTEGRATION] Timing Analysis:');
        print('  Time elapsed: ${stopwatch.elapsedMilliseconds}ms');
        print('  Processing timeout: 1000ms');
        print('  VideoRecordingCompleted received: '
            '${states.any((state) => state is Success || state is Failure)}');

        // Check if timeout was triggered
        final hasError = states.any((state) => state is Error);
        if (hasError) {
          final errorState = states.whereType<Error>().first;
          print('  Timeout error: ${errorState.error}');
        }

        await subscription.cancel();
      });
    });
  });
}
