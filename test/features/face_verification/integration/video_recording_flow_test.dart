import 'dart:io';

import 'package:bloomg_flutter/features/face_verification/bloc/face_video_capture_bloc.dart';
import 'package:bloomg_flutter/features/face_verification/models/video_capture_config.dart';
import 'package:bloomg_flutter/features/face_verification/repository/face_detection_repository.dart';
import 'package:bloomg_flutter/features/face_verification/repository/video_storage_repository.dart';
import 'package:bloomg_flutter/features/face_verification/services/video_validation_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

// Mock classes
class MockFaceDetectionRepository extends Mock
    implements FaceDetectionRepository {}

class MockVideoStorageRepository extends Mock
    implements VideoStorageRepository {}

class MockVideoValidationService extends Mock
    implements VideoValidationService {}

void main() {
  group('[FACE_VERIFICATION] Video Recording Flow Integration Tests', () {
    late FaceVideoCaptureBloc bloc;
    late MockFaceDetectionRepository mockFaceDetectionRepository;
    late MockVideoStorageRepository mockVideoStorageRepository;
    late MockVideoValidationService mockVideoValidationService;
    late Directory tempDir;

    setUpAll(() {
      registerFallbackValue(const VideoCaptureConfig());
    });

    setUp(() {
      mockFaceDetectionRepository = MockFaceDetectionRepository();
      mockVideoStorageRepository = MockVideoStorageRepository();
      mockVideoValidationService = MockVideoValidationService();
      tempDir = Directory.systemTemp.createTempSync('video_flow_test_');

      // Default mock behaviors
      when(() => mockFaceDetectionRepository.initialize())
          .thenAnswer((_) async {});
      when(() => mockVideoStorageRepository.initialize())
          .thenAnswer((_) async {});
      when(() => mockVideoStorageRepository.startRecording())
          .thenAnswer((_) async {});
      when(() => mockVideoStorageRepository.stopRecording())
          .thenAnswer((_) async {});
      when(() => mockVideoStorageRepository.currentVideoPath)
          .thenReturn('${tempDir.path}/test_video.mp4');

      bloc = FaceVideoCaptureBloc(
        faceDetectionRepository: mockFaceDetectionRepository,
        videoStorageRepository: mockVideoStorageRepository,
        videoValidationService: mockVideoValidationService,
      );
    });

    tearDown(() {
      bloc.close();
      tempDir.deleteSync(recursive: true);
    });

    group('[FACE_VERIFICATION] Complete Recording Flow', () {
      test(
          'should handle complete video recording flow with path synchronization',
          () async {
        // Arrange
        final testVideoPath = '${tempDir.path}/test_video.mp4';
        final testFile = File(testVideoPath);
        await testFile.writeAsBytes([1, 2, 3, 4]); // Create test file

        when(() => mockVideoStorageRepository.currentVideoPath)
            .thenReturn(testVideoPath);
        when(() => mockVideoValidationService.validateVideo(any()))
            .thenAnswer((_) async => true);

        final states = <FaceVideoCaptureState>[];
        final subscription = bloc.stream.listen(states.add);

        // Act
        bloc.add(const InitializeCamera());
        await Future<void>.delayed(const Duration(milliseconds: 100));

        bloc.add(const StartRecording());
        await Future<void>.delayed(const Duration(milliseconds: 100));

        bloc.add(const StopRecording());
        await Future<void>.delayed(const Duration(milliseconds: 100));

        // Simulate CamerAwesome completing video recording
        bloc.add(VideoRecordingCompleted(videoPath: testVideoPath));
        await Future<void>.delayed(const Duration(milliseconds: 100));

        // Assert
        expect(states, isNotEmpty);
        expect(states.any((state) => state is CameraReady), isTrue);
        expect(states.any((state) => state is Recording), isTrue);
        expect(states.any((state) => state is Processing), isTrue);

        verify(() => mockVideoStorageRepository.startRecording()).called(1);
        verify(() => mockVideoStorageRepository.stopRecording()).called(1);
        verify(() => mockVideoValidationService.validateVideo(testVideoPath))
            .called(1);

        await subscription.cancel();
      });

      test('should handle video recording with path mismatch', () async {
        // Arrange
        final expectedPath = '${tempDir.path}/expected_video.mp4';
        final actualPath = '${tempDir.path}/actual_video.mp4';
        final actualFile = File(actualPath);
        await actualFile.writeAsBytes([1, 2, 3, 4]); // Create test file

        when(() => mockVideoStorageRepository.currentVideoPath)
            .thenReturn(expectedPath);
        when(() => mockVideoValidationService.validateVideo(any()))
            .thenAnswer((_) async => true);

        final states = <FaceVideoCaptureState>[];
        final subscription = bloc.stream.listen(states.add);

        // Act
        bloc.add(const InitializeCamera());
        await Future<void>.delayed(const Duration(milliseconds: 100));

        bloc.add(const StartRecording());
        await Future<void>.delayed(const Duration(milliseconds: 100));

        bloc.add(const StopRecording());
        await Future<void>.delayed(const Duration(milliseconds: 100));

        // Simulate CamerAwesome saving to different path
        bloc.add(VideoRecordingCompleted(videoPath: actualPath));
        await Future<void>.delayed(const Duration(milliseconds: 100));

        // Assert
        expect(states, isNotEmpty);
        expect(states.any((state) => state is Processing), isTrue);

        verify(() => mockVideoValidationService.validateVideo(actualPath))
            .called(1);

        await subscription.cancel();
      });

      test('should handle video file not found error', () async {
        // Arrange
        const nonExistentPath = '/nonexistent/video.mp4';

        when(() => mockVideoStorageRepository.currentVideoPath)
            .thenReturn(nonExistentPath);

        final states = <FaceVideoCaptureState>[];
        final subscription = bloc.stream.listen(states.add);

        // Act
        bloc.add(const InitializeCamera());
        await Future<void>.delayed(const Duration(milliseconds: 100));

        bloc.add(const StartRecording());
        await Future<void>.delayed(const Duration(milliseconds: 100));

        bloc.add(const StopRecording());
        await Future<void>.delayed(const Duration(milliseconds: 100));

        // Simulate CamerAwesome completing with non-existent file
        bloc.add(const VideoRecordingCompleted(videoPath: nonExistentPath));
        await Future<void>.delayed(const Duration(milliseconds: 100));

        // Assert
        expect(states, isNotEmpty);
        expect(states.any((state) => state is Error), isTrue);

        final errorState = states.whereType<Error>().first;
        expect(errorState.error, contains('Video file not found'));

        await subscription.cancel();
      });

      test('should handle processing timeout', () async {
        // Arrange
        final testVideoPath = '${tempDir.path}/test_video.mp4';

        when(() => mockVideoStorageRepository.currentVideoPath)
            .thenReturn(testVideoPath);

        final states = <FaceVideoCaptureState>[];
        final subscription = bloc.stream.listen(states.add);

        // Act
        bloc.add(const InitializeCamera());
        await Future<void>.delayed(const Duration(milliseconds: 100));

        bloc.add(const StartRecording());
        await Future<void>.delayed(const Duration(milliseconds: 100));

        bloc.add(const StopRecording());
        await Future<void>.delayed(const Duration(milliseconds: 100));

        // Don't send VideoRecordingCompleted - let timeout trigger
        bloc.add(const ProcessingTimeout());
        await Future<void>.delayed(const Duration(milliseconds: 100));

        // Assert
        expect(states, isNotEmpty);
        expect(states.any((state) => state is Processing), isTrue);

        await subscription.cancel();
      });
    });

    group('[FACE_VERIFICATION] Error Recovery', () {
      test('should recover from repository initialization failure', () async {
        // Arrange
        when(() => mockVideoStorageRepository.initialize())
            .thenThrow(Exception('Repository init failed'));

        final states = <FaceVideoCaptureState>[];
        final subscription = bloc.stream.listen(states.add);

        // Act
        bloc.add(const InitializeCamera());
        await Future<void>.delayed(const Duration(milliseconds: 100));

        // Assert
        expect(states, isNotEmpty);
        expect(states.any((state) => state is Error), isTrue);

        final errorState = states.whereType<Error>().first;
        expect(errorState.error, contains('Failed to initialize camera'));

        await subscription.cancel();
      });

      test('should recover from recording start failure', () async {
        // Arrange
        when(() => mockVideoStorageRepository.startRecording())
            .thenThrow(Exception('Recording start failed'));

        final states = <FaceVideoCaptureState>[];
        final subscription = bloc.stream.listen(states.add);

        // Act
        bloc.add(const InitializeCamera());
        await Future<void>.delayed(const Duration(milliseconds: 100));

        bloc.add(const StartRecording());
        await Future<void>.delayed(const Duration(milliseconds: 100));

        // Assert
        expect(states, isNotEmpty);
        expect(states.any((state) => state is Error), isTrue);

        final errorState = states.whereType<Error>().first;
        expect(errorState.error, contains('Failed to start recording'));

        await subscription.cancel();
      });
    });
  });
}
